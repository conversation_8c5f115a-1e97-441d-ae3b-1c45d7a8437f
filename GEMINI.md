# Gemini CLI Notes for awe-labs

This project is an Nx monorepo. When running commands, prefer using `npx nx <command>` instead of `npm` directly.

Example:
- To run tests for all projects: `npx nx run-many --target=test --all`
- To build a specific project (e.g., `codashi-core`): `npx nx build codashi-core`
- To run tests for a specific project (e.g., `codashi-core`): `npx nx test codashi-core`
- To lint a specific project (e.g., `codashi-core`): `npx nx lint codashi-core`

