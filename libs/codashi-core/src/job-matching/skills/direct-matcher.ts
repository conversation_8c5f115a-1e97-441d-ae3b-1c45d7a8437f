import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../entities/job';
import { withTimeout } from '../../utils/common-utils';
import type { DirectSkillMatch, Skill, SkillAnalysisOptions } from './types';
import { synonymDetectionSchema } from './types';

/**
 * Represents a skill object from job skills
 */
export type SkillObject = NonNullable<Job['skills']>[number];

/**
 * Finds direct matches between single resume skills and job requirements.
 * Handles exact, synonym, and keyword-based matching.
 */
export class DirectMatcher {
  private model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  /**
   * Finds all direct matches between resume skills and job skills
   *
   * @param resumeSkills - Skills extracted from a single resume
   * @param jobSkills - Skills required by the job posting
   * @param options - Configuration options
   * @returns Array of direct skill matches
   */
  async findDirectMatches(
    resumeSkills: Skill[],
    jobSkills: SkillObject[],
    options: SkillAnalysisOptions = {}
  ): Promise<DirectSkillMatch[]> {
    if (!jobSkills || jobSkills.length === 0 || resumeSkills.length === 0) {
      return [];
    }

    const matches: DirectSkillMatch[] = [];
    const unmatchedResumeSkills: Skill[] = [];

    // First pass: exact matches
    for (const resumeSkill of resumeSkills) {
      let matched = false;

      for (const jobSkill of jobSkills) {
        if (this.isExactMatch(resumeSkill.name, jobSkill.name)) {
          matches.push({
            jobSkill: jobSkill.name,
            resumeSkill: resumeSkill.name,
            matchType: 'exact',
            source: resumeSkill.source,
            sourceSection: resumeSkill.sourceSection,
          });
          matched = true;
          break;
        }
      }

      if (!matched) {
        unmatchedResumeSkills.push(resumeSkill);
      }
    }

    // Second pass: keyword matches
    const keywordMatches = await this.findKeywordMatches(
      unmatchedResumeSkills,
      jobSkills,
      options
    );
    matches.push(...keywordMatches);

    // Filter out skills that were matched by keywords
    const keywordMatchedSkills = new Set(
      keywordMatches.map((match) => match.resumeSkill.toLowerCase())
    );
    const stillUnmatchedSkills = unmatchedResumeSkills.filter(
      (skill) => !keywordMatchedSkills.has(skill.name.toLowerCase())
    );

    // Third pass: synonym matches using AI
    const synonymMatches = await this.findSynonymMatches(
      stillUnmatchedSkills,
      jobSkills,
      options
    );
    matches.push(...synonymMatches);

    return matches;
  }

  /**
   * Checks if two skill names are exact matches (case-insensitive)
   */
  private isExactMatch(resumeSkill: string, jobSkill: string): boolean {
    return resumeSkill.toLowerCase().trim() === jobSkill.toLowerCase().trim();
  }

  /**
   * Finds keyword-based matches between resume and job skills
   */
  private async findKeywordMatches(
    resumeSkills: Skill[],
    jobSkills: SkillObject[],
    options: SkillAnalysisOptions
  ): Promise<DirectSkillMatch[]> {
    const matches: DirectSkillMatch[] = [];

    for (const resumeSkill of resumeSkills) {
      for (const jobSkill of jobSkills) {
        if (this.isKeywordMatch(resumeSkill, jobSkill)) {
          matches.push({
            jobSkill: jobSkill.name,
            resumeSkill: resumeSkill.name,
            matchType: 'keyword',
            source: resumeSkill.source,
            sourceSection: resumeSkill.sourceSection,
          });
          break; // Only match each resume skill once
        }
      }
    }

    return matches;
  }

  /**
   * Checks if a resume skill matches a job skill through keywords
   */
  private isKeywordMatch(
    resumeSkill: Skill,
    jobSkill: SkillObject
  ): boolean {
    const resumeKeywords = [
      resumeSkill.name.toLowerCase(),
      ...resumeSkill.keywords.map((k) => k.toLowerCase()),
    ];

    const jobKeywords = [
      jobSkill.name.toLowerCase(),
      ...(jobSkill.keywords?.map((k) => k.toLowerCase()) || []),
    ];

    // Check if any resume keyword matches any job keyword
    return resumeKeywords.some((resumeKeyword) =>
      jobKeywords.some((jobKeyword) => {
        // Direct substring match
        return (
          resumeKeyword.includes(jobKeyword) ||
          jobKeyword.includes(resumeKeyword)
        );
      })
    );
  }

  /**
   * Finds synonym matches using AI analysis
   */
  private async findSynonymMatches(
    resumeSkills: Skill[],
    jobSkills: SkillObject[],
    options: SkillAnalysisOptions
  ): Promise<DirectSkillMatch[]> {
    if (resumeSkills.length === 0 || !jobSkills?.length) {
      return [];
    }

    try {
      // Create skill pairs for analysis
      const skillPairs: Array<{
        resumeSkill: Skill;
        jobSkill: SkillObject;
      }> = [];

      for (const resumeSkill of resumeSkills) {
        for (const jobSkill of jobSkills) {
          skillPairs.push({ resumeSkill, jobSkill });
        }
      }

      // Process in batches to avoid overwhelming the AI model
      const batchSize = 10;
      const matches: DirectSkillMatch[] = [];

      for (let i = 0; i < skillPairs.length; i += batchSize) {
        const batch = skillPairs.slice(i, i + batchSize);
        const batchOperation = this.processSynonymBatch(batch, options);

        const batchMatches = options.timeoutMs
          ? await withTimeout(
              batchOperation,
              options.timeoutMs,
              `Synonym detection batch ${i / batchSize + 1}`
            )
          : await batchOperation;

        matches.push(...batchMatches);
      }

      return matches;
    } catch (error) {
      console.warn(
        'AI synonym detection failed, falling back to basic matching:',
        error
      );
      return [];
    }
  }

  /**
   * Processes a batch of skill pairs for synonym detection
   */
  private async processSynonymBatch(
    batch: Array<{
      resumeSkill: Skill;
      jobSkill: SkillObject;
    }>,
    options: SkillAnalysisOptions
  ): Promise<DirectSkillMatch[]> {
    const prompt = ChatPromptTemplate.fromTemplate(`
Analyze the following skill pairs to determine if they are synonyms or refer to the same technology/concept.
Consider abbreviations, alternative names, and closely related technologies.

Skill pairs to analyze:
{skill_pairs}

For each pair, determine if the resume skill and job skill are synonyms or refer to the same thing.
Be conservative - only mark as synonyms if they clearly refer to the same technology or concept.

Examples of synonyms:
- "JavaScript" and "JS"
- "React.js" and "React"
- "Node.js" and "NodeJS"
- "PostgreSQL" and "Postgres"

Examples of NOT synonyms:
- "React" and "Vue" (different frameworks)
- "Python" and "Java" (different languages)
- "MySQL" and "PostgreSQL" (different databases)

{format_instructions}
    `);

    const parser = StructuredOutputParser.fromZodSchema(synonymDetectionSchema);
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const skillPairsText = batch
      .map(
        ({ resumeSkill, jobSkill }) =>
          `- Resume: "${resumeSkill.name}" | Job: "${jobSkill.name}"`
      )
      .join('\n');

    const result = await chain.invoke({
      skill_pairs: skillPairsText,
      format_instructions: parser.getFormatInstructions(),
    });

    // Convert AI results to DirectSkillMatch format
    const matches: DirectSkillMatch[] = [];

    for (const match of result.matches) {
      if (match.isSynonym) {
        // Find the original resume skill to get source information
        const originalResumeSkill = batch.find(
          ({ resumeSkill }) => resumeSkill.name === match.resumeSkill
        )?.resumeSkill;

        if (originalResumeSkill) {
          matches.push({
            jobSkill: match.jobSkill,
            resumeSkill: match.resumeSkill,
            matchType: 'synonym',
            source: originalResumeSkill.source,
            sourceSection: originalResumeSkill.sourceSection,
          });
        }
      }
    }

    return matches;
  }

  /**
   * Gets unmatched skills for transferable skill analysis
   */
  getUnmatchedSkills(
    resumeSkills: Skill[],
    jobSkills: SkillObject[],
    directMatches: DirectSkillMatch[]
  ): {
    unmatchedResumeSkills: Skill[];
    unmatchedJobSkills: SkillObject[];
  } {
    const matchedResumeSkills = new Set(
      directMatches.map((match) => match.resumeSkill.toLowerCase())
    );
    const matchedJobSkills = new Set(
      directMatches.map((match) => match.jobSkill.toLowerCase())
    );

    const unmatchedResumeSkills = resumeSkills.filter(
      (skill) => !matchedResumeSkills.has(skill.name.toLowerCase())
    );

    const unmatchedJobSkills = (jobSkills || []).filter(
      (skill) => !matchedJobSkills.has(skill.name.toLowerCase())
    );

    return { unmatchedResumeSkills, unmatchedJobSkills };
  }
}
