import { z } from 'zod';

/**
 * Represents a single resume title analysis result.
 * This is the result of analyzing one resume against one job posting for title matching.
 */
export interface TitleAnalysis {
  /** The original title from the resume */
  originalTitle: string;
  /** The job title being matched against */
  jobTitle: string;
  /** Confidence score for the title match (0-1) */
  confidence: number;
  /** AI reasoning for the title match */
  reasoning: string;
  /** Suggested improved title (if any) */
  suggestedTitle?: string;
  /** Summary statistics for the title analysis */
  summary: {
    /** Whether the title is a good match for the job */
    isGoodMatch: boolean;
    /** Confidence level: 'low', 'medium', 'high' */
    confidenceLevel: 'low' | 'medium' | 'high';
    /** Whether an improvement was suggested */
    hasImprovement: boolean;
  };
}

/**
 * Options for configuring single-resume title analysis
 */
export interface TitleAnalysisOptions {
  /** Timeout in milliseconds for AI operations */
  timeoutMs?: number;
  /** Whether to suggest title improvements */
  suggestImprovements?: boolean;
  /** Minimum confidence threshold (0-1) */
  confidenceThreshold?: number;
}

/**
 * Error class for single-resume title matching operations
 */
export class TitleMatchError extends Error {
  constructor(message: string, public readonly cause?: unknown) {
    super(message);
    this.name = 'TitleMatchError';
  }
}

/**
 * Zod schema for title analysis AI response
 */
export const titleAnalysisSchema = z.object({
  confidence: z
    .number()
    .min(0)
    .max(1)
    .describe('Confidence score for the title match (0-1)'),
  reasoning: z
    .string()
    .describe('Detailed reasoning for the title match assessment'),
  isGoodMatch: z
    .boolean()
    .describe('Whether the title is a good match for the job'),
  suggestedTitle: z
    .string()
    .optional()
    .describe('Suggested improved title if improvements are needed'),
});

/**
 * Type for the AI response schema
 */
export type TitleAnalysisResponse = z.infer<typeof titleAnalysisSchema>;

/**
 * Combined result type for all single-resume analyses
 */
export interface AnalysisResult {
  /** Experience analysis result */
  experience: import('./experience/types').ExperienceAnalysis;
  /** Skills analysis result */
  skills: import('./skills/types').SkillAnalysis;
  /** Title analysis result */
  title: TitleAnalysis;
  /** Overall summary across all analysis types */
  overallSummary: {
    /** Total score combining all analysis types (0-100) */
    totalScore: number;
    /** Overall match quality: 'poor', 'fair', 'good', 'excellent' */
    matchQuality: 'poor' | 'fair' | 'good' | 'excellent';
    /** Key strengths identified */
    strengths: string[];
    /** Key areas for improvement */
    improvements: string[];
    /** Whether this resume is recommended for the job */
    isRecommended: boolean;
  };
}

/**
 * Options for configuring the complete single-resume analysis
 */
export interface AnalysisOptions {
  /** Experience analysis options */
  experience?: import('./experience/types').ExperienceAnalysisOptions;
  /** Skills analysis options */
  skills?: import('./skills/types').SkillAnalysisOptions;
  /** Title analysis options */
  title?: TitleAnalysisOptions;
  /** Whether to include detailed source information */
  includeSourceDetails?: boolean;
  /** Overall timeout for the complete analysis */
  timeoutMs?: number;
}

/**
 * Helper function to determine confidence level from numeric score
 */
export function getConfidenceLevel(
  confidence: number
): 'low' | 'medium' | 'high' {
  if (confidence >= 0.7) return 'high';
  if (confidence >= 0.4) return 'medium';
  return 'low';
}

/**
 * Helper function to calculate overall match quality
 */
export function calculateMatchQuality(
  experienceScore: number,
  skillsCoverage: number,
  titleConfidence: number
): 'poor' | 'fair' | 'good' | 'excellent' {
  // Weighted average: experience 40%, skills 40%, title 20%
  const overallScore =
    experienceScore * 0.4 + skillsCoverage * 0.4 + titleConfidence * 100 * 0.2;

  if (overallScore >= 80) return 'excellent';
  if (overallScore >= 65) return 'good';
  if (overallScore >= 45) return 'fair';
  return 'poor';
}

/**
 * Helper function to determine if a resume should be recommended
 */
export function shouldRecommendResume(
  matchQuality: 'poor' | 'fair' | 'good' | 'excellent',
  titleConfidence: number,
  skillsCoverage: number
): boolean {
  // Recommend if:
  // - Match quality is good or excellent, OR
  // - Match quality is fair AND (high title confidence OR good skills coverage)
  return (
    matchQuality === 'excellent' ||
    matchQuality === 'good' ||
    (matchQuality === 'fair' &&
      (titleConfidence >= 0.7 || skillsCoverage >= 60))
  );
}
